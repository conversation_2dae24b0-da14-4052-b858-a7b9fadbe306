// server/controllers/commentController.js
const Comment = require('../models/commentModel');
const Post = require('../models/postModel');

// Create a comment on a post
const createComment = async (req, res) => {
  const { postId } = req.params;
  const { text, author } = req.body; // Assuming author ID is sent in the request body

  if (!text || !author) {
    return res.status(400).json({ message: 'Missing required fields (text, author)' });
  }

  try {
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }

    const comment = new Comment({ text, author, post: postId });
    await comment.save();

    // Add comment reference to the post
    post.comments.push(comment._id);
    await post.save();

    res.status(201).json(comment);
  } catch (err) {
    console.error('Error creating comment:', err);
    res.status(500).json({ message: 'Error creating comment' });
  }
};

// Get all comments for a specific post
const getCommentsForPost = async (req, res) => {
  const { postId } = req.params;

  try {
    const comments = await Comment.find({ post: postId }).populate('author', 'username'); // Populate author username
    res.json(comments);
  } catch (err) {
    console.error('Error fetching comments:', err);
    res.status(500).json({ message: 'Error fetching comments' });
  }
};

module.exports = { createComment, getCommentsForPost };