// server/controllers/commentController.js
const Comment = require('../models/commentModel');
const Post = require('../models/postModel');

// Create a comment on a post
const createComment = async (req, res) => {
  const { postId } = req.params;
  const { body, author } = req.body; // Assuming author ID is sent in the request body

  if (!body || !author) {
    return res.status(400).json({ message: 'Missing required fields (body, author)' });
  }

  try {
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }

    const comment = new Comment({ body, author, postId });
    await comment.save();

    // Add comment reference to the post
    post.comments.push(comment._id);
    await post.save();

    res.status(201).json(comment);
  } catch (err) {
    console.error('Error creating comment:', err);
    res.status(500).json({ message: 'Error creating comment' });
  }
};

// Get all comments for a specific post
const getCommentsForPost = async (req, res) => {
  const { postId } = req.params;

  try {
    const comments = await Comment.find({ postId }).populate('author', 'username'); // Populate author username
    res.json(comments);
  } catch (err) {
    console.error('Error fetching comments:', err);
    res.status(500).json({ message: 'Error fetching comments' });
  }
};

module.exports = { createComment, getCommentsForPost };