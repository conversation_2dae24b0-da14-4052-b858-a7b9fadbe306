lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      bcryptjs:
        specifier: ^2.4.3
        version: 2.4.3
      cors:
        specifier: ^2.8.5
        version: 2.8.5
      dotenv:
        specifier: ^16.4.5
        version: 16.4.5
      express:
        specifier: ^4.21.1
        version: 4.21.1
      jsonwebtoken:
        specifier: ^9.0.2
        version: 9.0.2
      mongoose:
        specifier: ^8.7.1
        version: 8.7.1(@aws-sdk/credential-providers@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0)))(socks@2.8.3)
      morgan:
        specifier: ^1.10.0
        version: 1.10.0
    devDependencies:
      nodemon:
        specifier: ^3.1.7
        version: 3.1.7

packages:

  '@aws-crypto/sha256-browser@5.2.0':
    resolution: {integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==}

  '@aws-crypto/sha256-js@5.2.0':
    resolution: {integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution: {integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==}

  '@aws-crypto/util@5.2.0':
    resolution: {integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==}

  '@aws-sdk/client-cognito-identity@3.670.0':
    resolution: {integrity: sha512-4q/yYdtO/RisGdQ3a2E912YekIpQYvS4TYPYS/onCbTXW/7C8/Ha7yUEncE7Woou0MDXyoVh50UATcJEmUt0+Q==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso-oidc@3.670.0':
    resolution: {integrity: sha512-4qDK2L36Q4J1lfemaHHd9ZxqKRaos3STp44qPAHf/8QyX6Uk5sXgZNVO2yWM7SIEtVKwwBh/fZAsdBkGPBfZcw==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.670.0

  '@aws-sdk/client-sso@3.670.0':
    resolution: {integrity: sha512-J+oz6uSsDvk4pimMDnKJb1wsV216zTrejvMTIL4RhUD1QPIVVOpteTdUShcjZUIZnkcJZGI+cym/SFK0kuzTpg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sts@3.670.0':
    resolution: {integrity: sha512-bExrNo8ZVWorS3cjMZKQnA2HWqDmAzcZoSN/cPVoPFNkHwdl1lzPxvcLzmhpIr48JHgKfybBjrbluDZfIYeEog==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/core@3.667.0':
    resolution: {integrity: sha512-pMcDVI7Tmdsc8R3sDv0Omj/4iRParGY+uJtAfF669WnZfDfaBQaix2Mq7+Mu08vdjqO9K3gicFvjk9S1VLmOKA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-cognito-identity@3.670.0':
    resolution: {integrity: sha512-l41x9lZtZnzyQ6+8D3i7BwqwG1u7JTfHwJDZmsh+sIbrccLlJm7TfxkegOwUbzJ6JdzdigCIM1cKBc52O8EG9w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-env@3.667.0':
    resolution: {integrity: sha512-zZbrkkaPc54WXm+QAnpuv0LPNfsts0HPPd+oCECGs7IQRaFsGj187cwvPg9RMWDFZqpm64MdBDoA8OQHsqzYCw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-http@3.667.0':
    resolution: {integrity: sha512-sjtybFfERZWiqTY7fswBxKQLvUkiCucOWyqh3IaPo/4nE1PXRnaZCVG0+kRBPrYIxWqiVwytvZzMJy8sVZcG0A==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-ini@3.670.0':
    resolution: {integrity: sha512-TB1gacUj75leaTt2JsCTzygDSIk4ksv9uZoR7VenlgFPRktyOeT+fapwIVBeB2Qg7b9uxAY2K5XkKstDZyBEEw==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.670.0

  '@aws-sdk/credential-provider-node@3.670.0':
    resolution: {integrity: sha512-zwNrRYzubk4CaZ7zebeDhxsm8QtNWkbGKopZPOaZSnd5uqUGRcmx4ccVRngWUK68XDP44aEUWC8iU5Pc7btpHQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-process@3.667.0':
    resolution: {integrity: sha512-HZHnvop32fKgsNHkdhVaul7UzQ25sEc0j9yqA4bjhtbk0ECl42kj3f1pJ+ZU/YD9ut8lMJs/vVqiOdNThVdeBw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-sso@3.670.0':
    resolution: {integrity: sha512-5PkA8BOy4q57Vhe9AESoHKZ7vjRbElNPKjXA4qC01xY+DitClRFz4O3B9sMzFp0PHlz9nDVSXXKgq0yzF/nAag==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.667.0':
    resolution: {integrity: sha512-t8CFlZMD/1p/8Cli3rvRiTJpjr/8BO64gw166AHgFZYSN2h95L2l1tcW0jpsc3PprA32nLg1iQVKYt4WGM4ugw==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.667.0

  '@aws-sdk/credential-providers@3.670.0':
    resolution: {integrity: sha512-2O7Ditryao7/8pCS4GPP2pba/Ia/rruejKoI8STiSmdgccssHcaHtiJ3mYNkKtRUEdi19ulspfz1nU+Ew4x4fA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-host-header@3.667.0':
    resolution: {integrity: sha512-Z7fIAMQnPegs7JjAQvlOeWXwpMRfegh5eCoIP6VLJIeR6DLfYKbP35JBtt98R6DXslrN2RsbTogjbxPEDQfw1w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-logger@3.667.0':
    resolution: {integrity: sha512-PtTRNpNm/5c746jRgZCNg4X9xEJIwggkGJrF0GP9AB1ANg4pc/sF2Fvn1NtqPe9wtQ2stunJprnm5WkCHN7QiA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.667.0':
    resolution: {integrity: sha512-U5glWD3ehFohzpUpopLtmqAlDurGWo2wRGPNgi4SwhWU7UDt6LS7E/UvJjqC0CUrjlzOw+my2A+Ncf+fisMhxQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-user-agent@3.669.0':
    resolution: {integrity: sha512-K8ScPi45zjJrj5Y2gRqVsvKKQCQbvQBfYGcBw9ZOx9TTavH80bOCBjWg/GFnvs4f37tqVc1wMN2oGvcTF6HveQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/region-config-resolver@3.667.0':
    resolution: {integrity: sha512-iNr+JhhA902JMKHG9IwT9YdaEx6KGl6vjAL5BRNeOjfj4cZYMog6Lz/IlfOAltMtT0w88DAHDEFrBd2uO0l2eg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/token-providers@3.667.0':
    resolution: {integrity: sha512-ZecJlG8p6D4UTYlBHwOWX6nknVtw/OBJ3yPXTSajBjhUlj9lE2xvejI8gl4rqkyLXk7z3bki+KR4tATbMaM9yg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sso-oidc': ^3.667.0

  '@aws-sdk/types@3.667.0':
    resolution: {integrity: sha512-gYq0xCsqFfQaSL/yT1Gl1vIUjtsg7d7RhnUfsXaHt8xTxOKRTdH9GjbesBjXOzgOvB0W0vfssfreSNGFlOOMJg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-endpoints@3.667.0':
    resolution: {integrity: sha512-X22SYDAuQJWnkF1/q17pkX3nGw5XMD9YEUbmt87vUnRq7iyJ3JOpl6UKOBeUBaL838wA5yzdbinmCITJ/VZ1QA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-locate-window@3.568.0':
    resolution: {integrity: sha512-3nh4TINkXYr+H41QaPelCceEB2FXP3fxp93YZXB/kqJvX0U9j0N0Uk45gvsjmEPzG8XxkPEeLIfT2I1M7A6Lig==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-user-agent-browser@3.670.0':
    resolution: {integrity: sha512-iRynWWazqEcCKwGMcQcywKTDLdLvqts1Yx474U64I9OKQXXwhOwhXbF5CAPSRta86lkVNAVYJa/0Bsv45pNn1A==}

  '@aws-sdk/util-user-agent-node@3.669.0':
    resolution: {integrity: sha512-9jxCYrgggy2xd44ZASqI7AMiRVaSiFp+06Kg8BQSU0ijKpBJlwcsqIS8pDT/n6LxuOw2eV5ipvM2C0r1iKzrGA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@mongodb-js/saslprep@1.1.9':
    resolution: {integrity: sha512-tVkljjeEaAhCqTzajSdgbQ6gE6f3oneVwa3iXR6csiEwXXOFsiC6Uh9iAjAhXPtqa/XMDHWjjeNH/77m/Yq2dw==}

  '@smithy/abort-controller@3.1.5':
    resolution: {integrity: sha512-DhNPnqTqPoG8aZ5dWkFOgsuY+i0GQ3CI6hMmvCoduNsnU9gUZWZBwGfDQsTTB7NvFPkom1df7jMIJWU90kuXXg==}
    engines: {node: '>=16.0.0'}

  '@smithy/config-resolver@3.0.9':
    resolution: {integrity: sha512-5d9oBf40qC7n2xUoHmntKLdqsyTMMo/r49+eqSIjJ73eDfEtljAxEhzIQ3bkgXJtR3xiv7YzMT/3FF3ORkjWdg==}
    engines: {node: '>=16.0.0'}

  '@smithy/core@2.4.8':
    resolution: {integrity: sha512-x4qWk7p/a4dcf7Vxb2MODIf4OIcqNbK182WxRvZ/3oKPrf/6Fdic5sSElhO1UtXpWKBazWfqg0ZEK9xN1DsuHA==}
    engines: {node: '>=16.0.0'}

  '@smithy/credential-provider-imds@3.2.4':
    resolution: {integrity: sha512-S9bb0EIokfYEuar4kEbLta+ivlKCWOCFsLZuilkNy9i0uEUEHSi47IFLPaxqqCl+0ftKmcOTHayY5nQhAuq7+w==}
    engines: {node: '>=16.0.0'}

  '@smithy/fetch-http-handler@3.2.9':
    resolution: {integrity: sha512-hYNVQOqhFQ6vOpenifFME546f0GfJn2OiQ3M0FDmuUu8V/Uiwy2wej7ZXxFBNqdx0R5DZAqWM1l6VRhGz8oE6A==}

  '@smithy/hash-node@3.0.7':
    resolution: {integrity: sha512-SAGHN+QkrwcHFjfWzs/czX94ZEjPJ0CrWJS3M43WswDXVEuP4AVy9gJ3+AF6JQHZD13bojmuf/Ap/ItDeZ+Qfw==}
    engines: {node: '>=16.0.0'}

  '@smithy/invalid-dependency@3.0.7':
    resolution: {integrity: sha512-Bq00GsAhHeYSuZX8Kpu4sbI9agH2BNYnqUmmbTGWOhki9NVsWn2jFr896vvoTMH8KAjNX/ErC/8t5QHuEXG+IA==}

  '@smithy/is-array-buffer@2.2.0':
    resolution: {integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==}
    engines: {node: '>=14.0.0'}

  '@smithy/is-array-buffer@3.0.0':
    resolution: {integrity: sha512-+Fsu6Q6C4RSJiy81Y8eApjEB5gVtM+oFKTffg+jSuwtvomJJrhUJBu2zS8wjXSgH/g1MKEWrzyChTBe6clb5FQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-content-length@3.0.9':
    resolution: {integrity: sha512-t97PidoGElF9hTtLCrof32wfWMqC5g2SEJNxaVH3NjlatuNGsdxXRYO/t+RPnxA15RpYiS0f+zG7FuE2DeGgjA==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-endpoint@3.1.4':
    resolution: {integrity: sha512-/ChcVHekAyzUbyPRI8CzPPLj6y8QRAfJngWcLMgsWxKVzw/RzBV69mSOzJYDD3pRwushA1+5tHtPF8fjmzBnrQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-retry@3.0.23':
    resolution: {integrity: sha512-x9PbGXxkcXIpm6L26qRSCC+eaYcHwybRmqU8LO/WM2RRlW0g8lz6FIiKbKgGvHuoK3dLZRiQVSQJveiCzwnA5A==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-serde@3.0.7':
    resolution: {integrity: sha512-VytaagsQqtH2OugzVTq4qvjkLNbWehHfGcGr0JLJmlDRrNCeZoWkWsSOw1nhS/4hyUUWF/TLGGml4X/OnEep5g==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-stack@3.0.7':
    resolution: {integrity: sha512-EyTbMCdqS1DoeQsO4gI7z2Gzq1MoRFAeS8GkFYIwbedB7Lp5zlLHJdg+56tllIIG5Hnf9ZWX48YKSHlsKvugGA==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-config-provider@3.1.8':
    resolution: {integrity: sha512-E0rU0DglpeJn5ge64mk8wTGEXcQwmpUTY5Zr7IzTpDLmHKiIamINERNZYrPQjg58Ck236sEKSwRSHA4CwshU6Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-http-handler@3.2.4':
    resolution: {integrity: sha512-49reY3+JgLMFNm7uTAKBWiKCA6XSvkNp9FqhVmusm2jpVnHORYFeFZ704LShtqWfjZW/nhX+7Iexyb6zQfXYIQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/property-provider@3.1.7':
    resolution: {integrity: sha512-QfzLi1GPMisY7bAM5hOUqBdGYnY5S2JAlr201pghksrQv139f8iiiMalXtjczIP5f6owxFn3MINLNUNvUkgtPw==}
    engines: {node: '>=16.0.0'}

  '@smithy/protocol-http@4.1.4':
    resolution: {integrity: sha512-MlWK8eqj0JlpZBnWmjQLqmFp71Ug00P+m72/1xQB3YByXD4zZ+y9N4hYrR0EDmrUCZIkyATWHOXFgtavwGDTzQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-builder@3.0.7':
    resolution: {integrity: sha512-65RXGZZ20rzqqxTsChdqSpbhA6tdt5IFNgG6o7e1lnPVLCe6TNWQq4rTl4N87hTDD8mV4IxJJnvyE7brbnRkQw==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-parser@3.0.7':
    resolution: {integrity: sha512-Fouw4KJVWqqUVIu1gZW8BH2HakwLz6dvdrAhXeXfeymOBrZw+hcqaWs+cS1AZPVp4nlbeIujYrKA921ZW2WMPA==}
    engines: {node: '>=16.0.0'}

  '@smithy/service-error-classification@3.0.7':
    resolution: {integrity: sha512-91PRkTfiBf9hxkIchhRKJfl1rsplRDyBnmyFca3y0Z3x/q0JJN480S83LBd8R6sBCkm2bBbqw2FHp0Mbh+ecSA==}
    engines: {node: '>=16.0.0'}

  '@smithy/shared-ini-file-loader@3.1.8':
    resolution: {integrity: sha512-0NHdQiSkeGl0ICQKcJQ2lCOKH23Nb0EaAa7RDRId6ZqwXkw4LJyIyZ0t3iusD4bnKYDPLGy2/5e2rfUhrt0Acw==}
    engines: {node: '>=16.0.0'}

  '@smithy/signature-v4@4.2.0':
    resolution: {integrity: sha512-LafbclHNKnsorMgUkKm7Tk7oJ7xizsZ1VwqhGKqoCIrXh4fqDDp73fK99HOEEgcsQbtemmeY/BPv0vTVYYUNEQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/smithy-client@3.4.0':
    resolution: {integrity: sha512-nOfJ1nVQsxiP6srKt43r2My0Gp5PLWCW2ASqUioxIiGmu6d32v4Nekidiv5qOmmtzIrmaD+ADX5SKHUuhReeBQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/types@3.5.0':
    resolution: {integrity: sha512-QN0twHNfe8mNJdH9unwsCK13GURU7oEAZqkBI+rsvpv1jrmserO+WnLE7jidR9W/1dxwZ0u/CB01mV2Gms/K2Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/url-parser@3.0.7':
    resolution: {integrity: sha512-70UbSSR8J97c1rHZOWhl+VKiZDqHWxs/iW8ZHrHp5fCCPLSBE7GcUlUvKSle3Ca+J9LLbYCj/A79BxztBvAfpA==}

  '@smithy/util-base64@3.0.0':
    resolution: {integrity: sha512-Kxvoh5Qtt0CDsfajiZOCpJxgtPHXOKwmM+Zy4waD43UoEMA+qPxxa98aE/7ZhdnBFZFXMOiBR5xbcaMhLtznQQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-body-length-browser@3.0.0':
    resolution: {integrity: sha512-cbjJs2A1mLYmqmyVl80uoLTJhAcfzMOyPgjwAYusWKMdLeNtzmMz9YxNl3/jRLoxSS3wkqkf0jwNdtXWtyEBaQ==}

  '@smithy/util-body-length-node@3.0.0':
    resolution: {integrity: sha512-Tj7pZ4bUloNUP6PzwhN7K386tmSmEET9QtQg0TgdNOnxhZvCssHji+oZTUIuzxECRfG8rdm2PMw2WCFs6eIYkA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-buffer-from@2.2.0':
    resolution: {integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-buffer-from@3.0.0':
    resolution: {integrity: sha512-aEOHCgq5RWFbP+UDPvPot26EJHjOC+bRgse5A8V3FSShqd5E5UN4qc7zkwsvJPPAVsf73QwYcHN1/gt/rtLwQA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-config-provider@3.0.0':
    resolution: {integrity: sha512-pbjk4s0fwq3Di/ANL+rCvJMKM5bzAQdE5S/6RL5NXgMExFAi6UgQMPOm5yPaIWPpr+EOXKXRonJ3FoxKf4mCJQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-defaults-mode-browser@3.0.23':
    resolution: {integrity: sha512-Y07qslyRtXDP/C5aWKqxTPBl4YxplEELG3xRrz2dnAQ6Lq/FgNrcKWmV561nNaZmFH+EzeGOX3ZRMbU8p1T6Nw==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-defaults-mode-node@3.0.23':
    resolution: {integrity: sha512-9Y4WH7f0vnDGuHUa4lGX9e2p+sMwODibsceSV6rfkZOvMC+BY3StB2LdO1NHafpsyHJLpwAgChxQ38tFyd6vkg==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-endpoints@2.1.3':
    resolution: {integrity: sha512-34eACeKov6jZdHqS5hxBMJ4KyWKztTMulhuQ2UdOoP6vVxMLrOKUqIXAwJe/wiWMhXhydLW664B02CNpQBQ4Aw==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-hex-encoding@3.0.0':
    resolution: {integrity: sha512-eFndh1WEK5YMUYvy3lPlVmYY/fZcQE1D8oSf41Id2vCeIkKJXPcYDCZD+4+xViI6b1XSd7tE+s5AmXzz5ilabQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-middleware@3.0.7':
    resolution: {integrity: sha512-OVA6fv/3o7TMJTpTgOi1H5OTwnuUa8hzRzhSFDtZyNxi6OZ70L/FHattSmhE212I7b6WSOJAAmbYnvcjTHOJCA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-retry@3.0.7':
    resolution: {integrity: sha512-nh1ZO1vTeo2YX1plFPSe/OXaHkLAHza5jpokNiiKX2M5YpNUv6RxGJZhpfmiR4jSvVHCjIDmILjrxKmP+/Ghug==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-stream@3.1.9':
    resolution: {integrity: sha512-7YAR0Ub3MwTMjDfjnup4qa6W8gygZMxikBhFMPESi6ASsl/rZJhwLpF/0k9TuezScCojsM0FryGdz4LZtjKPPQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-uri-escape@3.0.0':
    resolution: {integrity: sha512-LqR7qYLgZTD7nWLBecUi4aqolw8Mhza9ArpNEQ881MJJIU2sE5iHCK6TdyqqzcDLy0OPe10IY4T8ctVdtynubg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-utf8@2.3.0':
    resolution: {integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@3.0.0':
    resolution: {integrity: sha512-rUeT12bxFnplYDe815GXbq/oixEGHfRFFtcTF3YdDi/JaENIM6aSYYLJydG83UNzLXeRI5K8abYd/8Sp/QM0kA==}
    engines: {node: '>=16.0.0'}

  '@types/webidl-conversions@7.0.3':
    resolution: {integrity: sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==}

  '@types/whatwg-url@11.0.5':
    resolution: {integrity: sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  basic-auth@2.0.1:
    resolution: {integrity: sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==}
    engines: {node: '>= 0.8'}

  bcryptjs@2.4.3:
    resolution: {integrity: sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  body-parser@1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  bson@6.8.0:
    resolution: {integrity: sha512-iOJg8pr7wq2tg/zSlCCHMi3hMm5JTOxLTagf3zxhcenHsFp+c6uOs6K7W5UE7A4QIJGtqh/ZovFNMP4mOPJynQ==}
    engines: {node: '>=16.20.1'}

  buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  dotenv@16.4.5:
    resolution: {integrity: sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==}
    engines: {node: '>=12'}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  express@4.21.1:
    resolution: {integrity: sha512-YSFlK1Ee0/GC8QaO91tHcDxJiE/X4FbpAyQWkxAvG6AXCuR65YzK8ua6D9hvi/TzUfZMpc+BwuM1IPw8fmQBiQ==}
    engines: {node: '>= 0.10.0'}

  fast-xml-parser@4.4.1:
    resolution: {integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==}
    hasBin: true

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  finalhandler@1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  ignore-by-default@1.0.1:
    resolution: {integrity: sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ip-address@9.0.5:
    resolution: {integrity: sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==}
    engines: {node: '>= 12'}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  jsbn@1.1.0:
    resolution: {integrity: sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==}

  jsonwebtoken@9.0.2:
    resolution: {integrity: sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==}
    engines: {node: '>=12', npm: '>=6'}

  jwa@1.4.1:
    resolution: {integrity: sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==}

  jws@3.2.2:
    resolution: {integrity: sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==}

  kareem@2.6.3:
    resolution: {integrity: sha512-C3iHfuGUXK2u8/ipq9LfjFfXFxAZMQJJq7vLS45r3D9Y2xQ/m4S8zaR4zMLFWh9AsNPXmcFfUDhTEO8UIC/V6Q==}
    engines: {node: '>=12.0.0'}

  lodash.includes@4.3.0:
    resolution: {integrity: sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==}

  lodash.isboolean@3.0.3:
    resolution: {integrity: sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==}

  lodash.isinteger@4.0.4:
    resolution: {integrity: sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==}

  lodash.isnumber@3.0.3:
    resolution: {integrity: sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}

  lodash.isstring@4.0.1:
    resolution: {integrity: sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==}

  lodash.once@4.1.1:
    resolution: {integrity: sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  memory-pager@1.5.0:
    resolution: {integrity: sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  mongodb-connection-string-url@3.0.1:
    resolution: {integrity: sha512-XqMGwRX0Lgn05TDB4PyG2h2kKO/FfWJyCzYQbIhXUxz7ETt0I/FqHjUeqj37irJ+Dl1ZtU82uYyj14u2XsZKfg==}

  mongodb@6.9.0:
    resolution: {integrity: sha512-UMopBVx1LmEUbW/QE0Hw18u583PEDVQmUmVzzBRH0o/xtE9DBRA5ZYLOjpLIa03i8FXjzvQECJcqoMvCXftTUA==}
    engines: {node: '>=16.20.1'}
    peerDependencies:
      '@aws-sdk/credential-providers': ^3.188.0
      '@mongodb-js/zstd': ^1.1.0
      gcp-metadata: ^5.2.0
      kerberos: ^2.0.1
      mongodb-client-encryption: '>=6.0.0 <7'
      snappy: ^7.2.2
      socks: ^2.7.1
    peerDependenciesMeta:
      '@aws-sdk/credential-providers':
        optional: true
      '@mongodb-js/zstd':
        optional: true
      gcp-metadata:
        optional: true
      kerberos:
        optional: true
      mongodb-client-encryption:
        optional: true
      snappy:
        optional: true
      socks:
        optional: true

  mongoose@8.7.1:
    resolution: {integrity: sha512-RpNMyhyzLVCVbf8xTVbrf/18G3MqQzNw5pJdvOJ60fzbCa3cOZzz9L+8XpqzBXtRlgZGWv0T7MmOtvrT8ocp1Q==}
    engines: {node: '>=16.20.1'}

  morgan@1.10.0:
    resolution: {integrity: sha512-AbegBVI4sh6El+1gNwvD5YIck7nSA36weD7xvIxG4in80j/UoK8AEGaWnnz8v1GxonMCltmlNs5ZKbGvl9b1XQ==}
    engines: {node: '>= 0.8.0'}

  mpath@0.9.0:
    resolution: {integrity: sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==}
    engines: {node: '>=4.0.0'}

  mquery@5.0.0:
    resolution: {integrity: sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==}
    engines: {node: '>=14.0.0'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  nodemon@3.1.7:
    resolution: {integrity: sha512-hLj7fuMow6f0lbB0cD14Lz2xNjwsyruH251Pk4t/yIitCFJbmY1myuLlHm/q06aST4jg6EgAh74PIBBrRqpVAQ==}
    engines: {node: '>=10'}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.2:
    resolution: {integrity: sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==}
    engines: {node: '>= 0.4'}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-to-regexp@0.1.10:
    resolution: {integrity: sha512-7lf7qcQidTku0Gu3YDPc8DJ1q7OOucfa/BSsIwjuh56VU7katFvuM8hULfkwB3Fns/rsVF7PwPKVw1sl5KQS9w==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  pstree.remy@1.1.8:
    resolution: {integrity: sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}

  sift@17.1.3:
    resolution: {integrity: sha512-Rtlj66/b0ICeFzYTuNvX/EF1igRbbnGSvEyT79McoZa/DeGhMyC5pWKOEsZKnpkqtSeovd5FL/bjHWC3CIIvCQ==}

  simple-update-notifier@2.0.0:
    resolution: {integrity: sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==}
    engines: {node: '>=10'}

  smart-buffer@4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  socks@2.8.3:
    resolution: {integrity: sha512-l5x7VUUWbjVFbafGLxPWkYsHIhEvmF85tbIeFZWc8ZPtoMyybuEhL7Jye/ooC4/d48FgOjSJXgsF/AJPYCW8Zw==}
    engines: {node: '>= 10.0.0', npm: '>= 3.0.0'}

  sparse-bitfield@3.0.3:
    resolution: {integrity: sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==}

  sprintf-js@1.1.3:
    resolution: {integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  touch@3.1.1:
    resolution: {integrity: sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==}
    hasBin: true

  tr46@4.1.1:
    resolution: {integrity: sha512-2lv/66T7e5yNyhAAC4NaKe5nVavzuGJQVVtRYLyQ2OI8tsJ61PMLlelehb0wi2Hx6+hT/OJUWZcw8MjlSRnxvw==}
    engines: {node: '>=14'}

  tslib@2.7.0:
    resolution: {integrity: sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  undefsafe@2.0.5:
    resolution: {integrity: sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==}
    engines: {node: '>=12'}

  whatwg-url@13.0.0:
    resolution: {integrity: sha512-9WWbymnqj57+XEuqADHrCJ2eSXzn8WXIW/YSGaZtb2WKAInQ6CHfaUUcTyyver0p8BDg5StLQq8h1vtZuwmOig==}
    engines: {node: '>=16'}

snapshots:

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.667.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.7.0
    optional: true

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.667.0
      tslib: 2.7.0
    optional: true

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/client-cognito-identity@3.670.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.670.0(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/client-sts': 3.670.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/credential-provider-node': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/middleware-host-header': 3.667.0
      '@aws-sdk/middleware-logger': 3.667.0
      '@aws-sdk/middleware-recursion-detection': 3.667.0
      '@aws-sdk/middleware-user-agent': 3.669.0
      '@aws-sdk/region-config-resolver': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@aws-sdk/util-endpoints': 3.667.0
      '@aws-sdk/util-user-agent-browser': 3.670.0
      '@aws-sdk/util-user-agent-node': 3.669.0
      '@smithy/config-resolver': 3.0.9
      '@smithy/core': 2.4.8
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.7
      '@smithy/invalid-dependency': 3.0.7
      '@smithy/middleware-content-length': 3.0.9
      '@smithy/middleware-endpoint': 3.1.4
      '@smithy/middleware-retry': 3.0.23
      '@smithy/middleware-serde': 3.0.7
      '@smithy/middleware-stack': 3.0.7
      '@smithy/node-config-provider': 3.1.8
      '@smithy/node-http-handler': 3.2.4
      '@smithy/protocol-http': 4.1.4
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/url-parser': 3.0.7
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.23
      '@smithy/util-defaults-mode-node': 3.0.23
      '@smithy/util-endpoints': 2.1.3
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-retry': 3.0.7
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt
    optional: true

  '@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0)':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sts': 3.670.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/credential-provider-node': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/middleware-host-header': 3.667.0
      '@aws-sdk/middleware-logger': 3.667.0
      '@aws-sdk/middleware-recursion-detection': 3.667.0
      '@aws-sdk/middleware-user-agent': 3.669.0
      '@aws-sdk/region-config-resolver': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@aws-sdk/util-endpoints': 3.667.0
      '@aws-sdk/util-user-agent-browser': 3.670.0
      '@aws-sdk/util-user-agent-node': 3.669.0
      '@smithy/config-resolver': 3.0.9
      '@smithy/core': 2.4.8
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.7
      '@smithy/invalid-dependency': 3.0.7
      '@smithy/middleware-content-length': 3.0.9
      '@smithy/middleware-endpoint': 3.1.4
      '@smithy/middleware-retry': 3.0.23
      '@smithy/middleware-serde': 3.0.7
      '@smithy/middleware-stack': 3.0.7
      '@smithy/node-config-provider': 3.1.8
      '@smithy/node-http-handler': 3.2.4
      '@smithy/protocol-http': 4.1.4
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/url-parser': 3.0.7
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.23
      '@smithy/util-defaults-mode-node': 3.0.23
      '@smithy/util-endpoints': 2.1.3
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-retry': 3.0.7
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt
    optional: true

  '@aws-sdk/client-sso@3.670.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/middleware-host-header': 3.667.0
      '@aws-sdk/middleware-logger': 3.667.0
      '@aws-sdk/middleware-recursion-detection': 3.667.0
      '@aws-sdk/middleware-user-agent': 3.669.0
      '@aws-sdk/region-config-resolver': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@aws-sdk/util-endpoints': 3.667.0
      '@aws-sdk/util-user-agent-browser': 3.670.0
      '@aws-sdk/util-user-agent-node': 3.669.0
      '@smithy/config-resolver': 3.0.9
      '@smithy/core': 2.4.8
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.7
      '@smithy/invalid-dependency': 3.0.7
      '@smithy/middleware-content-length': 3.0.9
      '@smithy/middleware-endpoint': 3.1.4
      '@smithy/middleware-retry': 3.0.23
      '@smithy/middleware-serde': 3.0.7
      '@smithy/middleware-stack': 3.0.7
      '@smithy/node-config-provider': 3.1.8
      '@smithy/node-http-handler': 3.2.4
      '@smithy/protocol-http': 4.1.4
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/url-parser': 3.0.7
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.23
      '@smithy/util-defaults-mode-node': 3.0.23
      '@smithy/util-endpoints': 2.1.3
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-retry': 3.0.7
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt
    optional: true

  '@aws-sdk/client-sts@3.670.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.670.0(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/credential-provider-node': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/middleware-host-header': 3.667.0
      '@aws-sdk/middleware-logger': 3.667.0
      '@aws-sdk/middleware-recursion-detection': 3.667.0
      '@aws-sdk/middleware-user-agent': 3.669.0
      '@aws-sdk/region-config-resolver': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@aws-sdk/util-endpoints': 3.667.0
      '@aws-sdk/util-user-agent-browser': 3.670.0
      '@aws-sdk/util-user-agent-node': 3.669.0
      '@smithy/config-resolver': 3.0.9
      '@smithy/core': 2.4.8
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.7
      '@smithy/invalid-dependency': 3.0.7
      '@smithy/middleware-content-length': 3.0.9
      '@smithy/middleware-endpoint': 3.1.4
      '@smithy/middleware-retry': 3.0.23
      '@smithy/middleware-serde': 3.0.7
      '@smithy/middleware-stack': 3.0.7
      '@smithy/node-config-provider': 3.1.8
      '@smithy/node-http-handler': 3.2.4
      '@smithy/protocol-http': 4.1.4
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/url-parser': 3.0.7
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.23
      '@smithy/util-defaults-mode-node': 3.0.23
      '@smithy/util-endpoints': 2.1.3
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-retry': 3.0.7
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt
    optional: true

  '@aws-sdk/core@3.667.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/core': 2.4.8
      '@smithy/node-config-provider': 3.1.8
      '@smithy/property-provider': 3.1.7
      '@smithy/protocol-http': 4.1.4
      '@smithy/signature-v4': 4.2.0
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/util-middleware': 3.0.7
      fast-xml-parser: 4.4.1
      tslib: 2.7.0
    optional: true

  '@aws-sdk/credential-provider-cognito-identity@3.670.0':
    dependencies:
      '@aws-sdk/client-cognito-identity': 3.670.0
      '@aws-sdk/types': 3.667.0
      '@smithy/property-provider': 3.1.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt
    optional: true

  '@aws-sdk/credential-provider-env@3.667.0':
    dependencies:
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@smithy/property-provider': 3.1.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/credential-provider-http@3.667.0':
    dependencies:
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/node-http-handler': 3.2.4
      '@smithy/property-provider': 3.1.7
      '@smithy/protocol-http': 4.1.4
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/util-stream': 3.1.9
      tslib: 2.7.0
    optional: true

  '@aws-sdk/credential-provider-ini@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)':
    dependencies:
      '@aws-sdk/client-sts': 3.670.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/credential-provider-env': 3.667.0
      '@aws-sdk/credential-provider-http': 3.667.0
      '@aws-sdk/credential-provider-process': 3.667.0
      '@aws-sdk/credential-provider-sso': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))
      '@aws-sdk/credential-provider-web-identity': 3.667.0(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/types': 3.667.0
      '@smithy/credential-provider-imds': 3.2.4
      '@smithy/property-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt
    optional: true

  '@aws-sdk/credential-provider-node@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.667.0
      '@aws-sdk/credential-provider-http': 3.667.0
      '@aws-sdk/credential-provider-ini': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/credential-provider-process': 3.667.0
      '@aws-sdk/credential-provider-sso': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))
      '@aws-sdk/credential-provider-web-identity': 3.667.0(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/types': 3.667.0
      '@smithy/credential-provider-imds': 3.2.4
      '@smithy/property-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - '@aws-sdk/client-sts'
      - aws-crt
    optional: true

  '@aws-sdk/credential-provider-process@3.667.0':
    dependencies:
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@smithy/property-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/credential-provider-sso@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))':
    dependencies:
      '@aws-sdk/client-sso': 3.670.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/token-providers': 3.667.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))
      '@aws-sdk/types': 3.667.0
      '@smithy/property-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt
    optional: true

  '@aws-sdk/credential-provider-web-identity@3.667.0(@aws-sdk/client-sts@3.670.0)':
    dependencies:
      '@aws-sdk/client-sts': 3.670.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@smithy/property-provider': 3.1.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/credential-providers@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))':
    dependencies:
      '@aws-sdk/client-cognito-identity': 3.670.0
      '@aws-sdk/client-sso': 3.670.0
      '@aws-sdk/client-sts': 3.670.0
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/credential-provider-cognito-identity': 3.670.0
      '@aws-sdk/credential-provider-env': 3.667.0
      '@aws-sdk/credential-provider-http': 3.667.0
      '@aws-sdk/credential-provider-ini': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/credential-provider-node': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/credential-provider-process': 3.667.0
      '@aws-sdk/credential-provider-sso': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))
      '@aws-sdk/credential-provider-web-identity': 3.667.0(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/types': 3.667.0
      '@smithy/credential-provider-imds': 3.2.4
      '@smithy/property-provider': 3.1.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt
    optional: true

  '@aws-sdk/middleware-host-header@3.667.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/protocol-http': 4.1.4
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/middleware-logger@3.667.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/middleware-recursion-detection@3.667.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/protocol-http': 4.1.4
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/middleware-user-agent@3.669.0':
    dependencies:
      '@aws-sdk/core': 3.667.0
      '@aws-sdk/types': 3.667.0
      '@aws-sdk/util-endpoints': 3.667.0
      '@smithy/core': 2.4.8
      '@smithy/protocol-http': 4.1.4
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/region-config-resolver@3.667.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/node-config-provider': 3.1.8
      '@smithy/types': 3.5.0
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.7
      tslib: 2.7.0
    optional: true

  '@aws-sdk/token-providers@3.667.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))':
    dependencies:
      '@aws-sdk/client-sso-oidc': 3.670.0(@aws-sdk/client-sts@3.670.0)
      '@aws-sdk/types': 3.667.0
      '@smithy/property-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/types@3.667.0':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/util-endpoints@3.667.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/types': 3.5.0
      '@smithy/util-endpoints': 2.1.3
      tslib: 2.7.0
    optional: true

  '@aws-sdk/util-locate-window@3.568.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@aws-sdk/util-user-agent-browser@3.670.0':
    dependencies:
      '@aws-sdk/types': 3.667.0
      '@smithy/types': 3.5.0
      bowser: 2.11.0
      tslib: 2.7.0
    optional: true

  '@aws-sdk/util-user-agent-node@3.669.0':
    dependencies:
      '@aws-sdk/middleware-user-agent': 3.669.0
      '@aws-sdk/types': 3.667.0
      '@smithy/node-config-provider': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@mongodb-js/saslprep@1.1.9':
    dependencies:
      sparse-bitfield: 3.0.3

  '@smithy/abort-controller@3.1.5':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/config-resolver@3.0.9':
    dependencies:
      '@smithy/node-config-provider': 3.1.8
      '@smithy/types': 3.5.0
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.7
      tslib: 2.7.0
    optional: true

  '@smithy/core@2.4.8':
    dependencies:
      '@smithy/middleware-endpoint': 3.1.4
      '@smithy/middleware-retry': 3.0.23
      '@smithy/middleware-serde': 3.0.7
      '@smithy/protocol-http': 4.1.4
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/credential-provider-imds@3.2.4':
    dependencies:
      '@smithy/node-config-provider': 3.1.8
      '@smithy/property-provider': 3.1.7
      '@smithy/types': 3.5.0
      '@smithy/url-parser': 3.0.7
      tslib: 2.7.0
    optional: true

  '@smithy/fetch-http-handler@3.2.9':
    dependencies:
      '@smithy/protocol-http': 4.1.4
      '@smithy/querystring-builder': 3.0.7
      '@smithy/types': 3.5.0
      '@smithy/util-base64': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/hash-node@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/invalid-dependency@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/is-array-buffer@3.0.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/middleware-content-length@3.0.9':
    dependencies:
      '@smithy/protocol-http': 4.1.4
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/middleware-endpoint@3.1.4':
    dependencies:
      '@smithy/middleware-serde': 3.0.7
      '@smithy/node-config-provider': 3.1.8
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      '@smithy/url-parser': 3.0.7
      '@smithy/util-middleware': 3.0.7
      tslib: 2.7.0
    optional: true

  '@smithy/middleware-retry@3.0.23':
    dependencies:
      '@smithy/node-config-provider': 3.1.8
      '@smithy/protocol-http': 4.1.4
      '@smithy/service-error-classification': 3.0.7
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-retry': 3.0.7
      tslib: 2.7.0
      uuid: 9.0.1
    optional: true

  '@smithy/middleware-serde@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/middleware-stack@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/node-config-provider@3.1.8':
    dependencies:
      '@smithy/property-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/node-http-handler@3.2.4':
    dependencies:
      '@smithy/abort-controller': 3.1.5
      '@smithy/protocol-http': 4.1.4
      '@smithy/querystring-builder': 3.0.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/property-provider@3.1.7':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/protocol-http@4.1.4':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/querystring-builder@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      '@smithy/util-uri-escape': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/querystring-parser@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/service-error-classification@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
    optional: true

  '@smithy/shared-ini-file-loader@3.1.8':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/signature-v4@4.2.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/protocol-http': 4.1.4
      '@smithy/types': 3.5.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-middleware': 3.0.7
      '@smithy/util-uri-escape': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/smithy-client@3.4.0':
    dependencies:
      '@smithy/middleware-endpoint': 3.1.4
      '@smithy/middleware-stack': 3.0.7
      '@smithy/protocol-http': 4.1.4
      '@smithy/types': 3.5.0
      '@smithy/util-stream': 3.1.9
      tslib: 2.7.0
    optional: true

  '@smithy/types@3.5.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/url-parser@3.0.7':
    dependencies:
      '@smithy/querystring-parser': 3.0.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-base64@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-body-length-browser@3.0.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/util-body-length-node@3.0.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-buffer-from@3.0.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-config-provider@3.0.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/util-defaults-mode-browser@3.0.23':
    dependencies:
      '@smithy/property-provider': 3.1.7
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      bowser: 2.11.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-defaults-mode-node@3.0.23':
    dependencies:
      '@smithy/config-resolver': 3.0.9
      '@smithy/credential-provider-imds': 3.2.4
      '@smithy/node-config-provider': 3.1.8
      '@smithy/property-provider': 3.1.7
      '@smithy/smithy-client': 3.4.0
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-endpoints@2.1.3':
    dependencies:
      '@smithy/node-config-provider': 3.1.8
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-hex-encoding@3.0.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/util-middleware@3.0.7':
    dependencies:
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-retry@3.0.7':
    dependencies:
      '@smithy/service-error-classification': 3.0.7
      '@smithy/types': 3.5.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-stream@3.1.9':
    dependencies:
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/node-http-handler': 3.2.4
      '@smithy/types': 3.5.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-uri-escape@3.0.0':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.7.0
    optional: true

  '@smithy/util-utf8@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      tslib: 2.7.0
    optional: true

  '@types/webidl-conversions@7.0.3': {}

  '@types/whatwg-url@11.0.5':
    dependencies:
      '@types/webidl-conversions': 7.0.3

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  array-flatten@1.1.1: {}

  balanced-match@1.0.2: {}

  basic-auth@2.0.1:
    dependencies:
      safe-buffer: 5.1.2

  bcryptjs@2.4.3: {}

  binary-extensions@2.3.0: {}

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bowser@2.11.0:
    optional: true

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  bson@6.8.0: {}

  buffer-equal-constant-time@1.0.1: {}

  bytes@3.1.2: {}

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  concat-map@0.0.1: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.3.7(supports-color@5.5.0):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 5.5.0

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  depd@2.0.0: {}

  destroy@1.2.0: {}

  dotenv@16.4.5: {}

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  ee-first@1.1.1: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  escape-html@1.0.3: {}

  etag@1.8.1: {}

  express@4.21.1:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.10
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.0.5
    optional: true

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  forwarded@0.2.0: {}

  fresh@0.5.2: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  has-flag@3.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  ignore-by-default@1.0.1: {}

  inherits@2.0.4: {}

  ip-address@9.0.5:
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3
    optional: true

  ipaddr.js@1.9.1: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-extglob@2.1.1: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  jsbn@1.1.0:
    optional: true

  jsonwebtoken@9.0.2:
    dependencies:
      jws: 3.2.2
      lodash.includes: 4.3.0
      lodash.isboolean: 3.0.3
      lodash.isinteger: 4.0.4
      lodash.isnumber: 3.0.3
      lodash.isplainobject: 4.0.6
      lodash.isstring: 4.0.1
      lodash.once: 4.1.1
      ms: 2.1.3
      semver: 7.6.3

  jwa@1.4.1:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jws@3.2.2:
    dependencies:
      jwa: 1.4.1
      safe-buffer: 5.2.1

  kareem@2.6.3: {}

  lodash.includes@4.3.0: {}

  lodash.isboolean@3.0.3: {}

  lodash.isinteger@4.0.4: {}

  lodash.isnumber@3.0.3: {}

  lodash.isplainobject@4.0.6: {}

  lodash.isstring@4.0.1: {}

  lodash.once@4.1.1: {}

  media-typer@0.3.0: {}

  memory-pager@1.5.0: {}

  merge-descriptors@1.0.3: {}

  methods@1.1.2: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  mongodb-connection-string-url@3.0.1:
    dependencies:
      '@types/whatwg-url': 11.0.5
      whatwg-url: 13.0.0

  mongodb@6.9.0(@aws-sdk/credential-providers@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0)))(socks@2.8.3):
    dependencies:
      '@mongodb-js/saslprep': 1.1.9
      bson: 6.8.0
      mongodb-connection-string-url: 3.0.1
    optionalDependencies:
      '@aws-sdk/credential-providers': 3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0))
      socks: 2.8.3

  mongoose@8.7.1(@aws-sdk/credential-providers@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0)))(socks@2.8.3):
    dependencies:
      bson: 6.8.0
      kareem: 2.6.3
      mongodb: 6.9.0(@aws-sdk/credential-providers@3.670.0(@aws-sdk/client-sso-oidc@3.670.0(@aws-sdk/client-sts@3.670.0)))(socks@2.8.3)
      mpath: 0.9.0
      mquery: 5.0.0
      ms: 2.1.3
      sift: 17.1.3
    transitivePeerDependencies:
      - '@aws-sdk/credential-providers'
      - '@mongodb-js/zstd'
      - gcp-metadata
      - kerberos
      - mongodb-client-encryption
      - snappy
      - socks
      - supports-color

  morgan@1.10.0:
    dependencies:
      basic-auth: 2.0.1
      debug: 2.6.9
      depd: 2.0.0
      on-finished: 2.3.0
      on-headers: 1.0.2
    transitivePeerDependencies:
      - supports-color

  mpath@0.9.0: {}

  mquery@5.0.0:
    dependencies:
      debug: 4.3.7(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  ms@2.0.0: {}

  ms@2.1.3: {}

  negotiator@0.6.3: {}

  nodemon@3.1.7:
    dependencies:
      chokidar: 3.6.0
      debug: 4.3.7(supports-color@5.5.0)
      ignore-by-default: 1.0.1
      minimatch: 3.1.2
      pstree.remy: 1.1.8
      semver: 7.6.3
      simple-update-notifier: 2.0.0
      supports-color: 5.5.0
      touch: 3.1.1
      undefsafe: 2.0.5

  normalize-path@3.0.0: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.2: {}

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  parseurl@1.3.3: {}

  path-to-regexp@0.1.10: {}

  picomatch@2.3.1: {}

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  pstree.remy@1.1.8: {}

  punycode@2.3.1: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.0.6

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  semver@7.6.3: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  setprototypeof@1.2.0: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2

  sift@17.1.3: {}

  simple-update-notifier@2.0.0:
    dependencies:
      semver: 7.6.3

  smart-buffer@4.2.0:
    optional: true

  socks@2.8.3:
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0
    optional: true

  sparse-bitfield@3.0.3:
    dependencies:
      memory-pager: 1.5.0

  sprintf-js@1.1.3:
    optional: true

  statuses@2.0.1: {}

  strnum@1.0.5:
    optional: true

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  touch@3.1.1: {}

  tr46@4.1.1:
    dependencies:
      punycode: 2.3.1

  tslib@2.7.0:
    optional: true

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  undefsafe@2.0.5: {}

  unpipe@1.0.0: {}

  utils-merge@1.0.1: {}

  uuid@9.0.1:
    optional: true

  vary@1.1.2: {}

  webidl-conversions@7.0.0: {}

  whatwg-url@13.0.0:
    dependencies:
      tr46: 4.1.1
      webidl-conversions: 7.0.0
