const mongoose = require('mongoose');

const postSchema = new mongoose.Schema({
  title: { type: String, required: true },
  body: { type: String, required: true },
  author: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  upvotes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],  // Array of users who upvoted
  downvotes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],  // Array of users who downvoted
  comments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Comment' }],  // Array of comment IDs
  createdAt: { type: Date, default: Date.now },
});

module.exports = mongoose.model('Post', postSchema);
