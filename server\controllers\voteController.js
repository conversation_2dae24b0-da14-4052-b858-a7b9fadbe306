// server/controllers/voteController.js
const Post = require('../models/postModel');

// Upvote a post
const upvotePost = async (req, res) => {
  const { postId } = req.params;
  const { userId } = req.body; // Assuming userId is sent in the request body

  if (!userId) {
    return res.status(400).json({ message: 'User ID is required to upvote.' });
  }

  try {
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }

    // Basic logic: Add user to upvotes, remove from downvotes if present
    // More complex logic (e.g., preventing double voting) can be added
    if (!post.upvotes.includes(userId)) {
      post.upvotes.push(userId);
    }
    post.downvotes = post.downvotes.filter(id => id.toString() !== userId);

    await post.save();
    res.json({ message: 'Post upvoted successfully', upvotes: post.upvotes.length, downvotes: post.downvotes.length });

  } catch (err) {
    console.error('Error upvoting post:', err);
    res.status(500).json({ message: 'Error upvoting post' });
  }
};

// Downvote a post
const downvotePost = async (req, res) => {
  const { postId } = req.params;
  const { userId } = req.body; // Assuming userId is sent in the request body

  if (!userId) {
    return res.status(400).json({ message: 'User ID is required to downvote.' });
  }

  try {
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }

    // Basic logic: Add user to downvotes, remove from upvotes if present
    if (!post.downvotes.includes(userId)) {
      post.downvotes.push(userId);
    }
    post.upvotes = post.upvotes.filter(id => id.toString() !== userId);

    await post.save();
    res.json({ message: 'Post downvoted successfully', upvotes: post.upvotes.length, downvotes: post.downvotes.length });

  } catch (err) {
    console.error('Error downvoting post:', err);
    res.status(500).json({ message: 'Error downvoting post' });
  }
};

module.exports = { upvotePost, downvotePost };