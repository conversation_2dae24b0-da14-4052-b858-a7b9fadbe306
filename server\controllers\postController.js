// controllers/postController.js
const Post = require('../models/postModel');

// Create a post
const createPost = async (req, res) => {
  const { title, body, author } = req.body;

  if (!title || !body || !author) {
    return res.status(400).json({ message: 'Missing required fields' });
  }

  try {
    const post = new Post({ title, body, author });
    await post.save();
    res.status(201).json(post);
  } catch (err) {
    res.status(500).json({ message: 'Error creating post' });
  }
};

// Get all posts
const getPosts = async (req, res) => {
  try {
    const posts = await Post.find().populate('author', 'username');
    res.json(posts);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching posts' });
  }
};

module.exports = { createPost, getPosts };
