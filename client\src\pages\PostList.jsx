import { useState, useEffect} from 'react';
import { Link } from 'react-router-dom'

import axios from 'axios';

const PostList = () => {
    const [posts, setPosts] = useState([])

    useEffect(() => {
        const fetchPosts = async () => {
            try {
                const resp = await axios.get('/api/posts')
                setPosts(resp.data)
            } catch (err) {
                console.error('Error fetching posts:', err);
            }
        };
        fetchPosts();
    }, [])

    return(
        <div>
        <h1>Posts</h1>
        {posts.map((post) => (
            <div key={post._id}>
                <h2>{post.title}</h2>
                <Link to={`/posts/${post._id}`}>Read more</Link>
            </div>
        ))}
        </div>
    )
}

export default PostList