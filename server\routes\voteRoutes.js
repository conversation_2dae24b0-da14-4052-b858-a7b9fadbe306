// server/routes/voteRoutes.js
const express = require('express');
// Import vote controller functions
const { upvotePost, downvotePost } = require('../controllers/voteController');

const router = express.Router();

// Define actual routes
router.post('/upvote/:postId', upvotePost); // Route to upvote a specific post
router.post('/downvote/:postId', downvotePost); // Route to downvote a specific post

module.exports = router;