// server/routes/commentRoutes.js
const express = require('express');
// Import comment controller functions
const { createComment, getCommentsForPost } = require('../controllers/commentController');

const router = express.Router();

// Define actual routes
router.post('/post/:postId', createComment); // Route to create a comment on a specific post
router.get('/post/:postId', getCommentsForPost); // Route to get comments for a specific post

module.exports = router;